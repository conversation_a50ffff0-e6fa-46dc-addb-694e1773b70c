# 果仁网多因子选股策略完整实施指南

## 策略概述

**策略名称**：多因子估值技术选股策略
**策略类型**：基于估值和技术指标的多因子选股策略，结合大盘择时和行业分散配置
**预期收益**：年化收益15-25%，最大回撤控制在20%以内

### 策略核心逻辑
1. **大盘择时**：根据大盘市净率判断买入时机
2. **多因子评分**：PE、PB、涨幅三维度评分系统（总分300分）
3. **行业分散**：每个行业仓位不超过3%
4. **分批卖出**：基于涨幅的止盈策略

## 第一步：登录和基础设置

### 1.1 登录果仁网
1. 打开浏览器，访问 https://guorn.com
2. 点击右上角"登录"按钮
3. 输入用户名和密码完成登录
4. 确认登录成功，页面显示用户信息

### 1.2 进入策略创建页面
1. 点击顶部导航栏"创建策略"
2. 页面跳转到策略创建界面
3. 确认页面加载完成，显示选股条件、排名条件等设置区域

## 第二步：创建自定义函数

### 2.1 进入自定义函数创建界面
1. 在策略创建页面左侧找到"选股指标"区域
2. 在"自定义"分类下点击"新建"按钮（+图标）
3. 弹出"新建自定义股票指标"对话框

### 2.2 创建PE评分函数
**函数配置：**
- **名称**：`PE评分`
- **类型**：公式
- **公式**：`Max(0, Min(100, 100 - (滚动市盈率 - 10) * 100 / 15))`
- **显示单位**：无
- **小数位数**：2
- **说明**：PE评分函数，25倍PE=0分，10倍PE=100分，线性插值

**操作步骤：**
1. 在名称框输入：PE评分
2. 类型保持默认"公式"
3. 在公式输入框输入：`Max(0, Min(100, 100 - (滚动市盈率 - 10) * 100 / 15))`
4. 显示单位选择"无"
5. 小数位数选择"2"
6. 说明框输入：PE评分函数，25倍PE=0分，10倍PE=100分，线性插值
7. 点击"保存"按钮

### 2.3 创建PB评分函数
**函数配置：**
- **名称**：`PB评分`
- **类型**：公式
- **公式**：`Max(0, Min(100, 100 - (市净率 - 1) * 100 / 1.5))`
- **显示单位**：无
- **小数位数**：2
- **说明**：PB评分函数，2.5倍PB=0分，1倍PB=100分，线性插值

**操作步骤：**
1. 重新点击"新建"按钮创建第二个函数
2. 在名称框输入：PB评分
3. 在公式输入框输入：`Max(0, Min(100, 100 - (市净率 - 1) * 100 / 1.5))`
4. 其他设置同PE评分函数
5. 说明框输入：PB评分函数，2.5倍PB=0分，1倍PB=100分，线性插值
6. 点击"保存"按钮

### 2.4 创建涨幅评分函数
**函数配置：**
- **名称**：`涨幅评分`
- **类型**：公式
- **公式**：`Max(0, Min(100, 100 - (收盘价/Min(收盘价, 252) - 1) * 200))`
- **显示单位**：无
- **小数位数**：2
- **说明**：涨幅评分函数，50%涨幅=0分，0%涨幅=100分，基于一年内最低价

**操作步骤：**
1. 重新点击"新建"按钮创建第三个函数
2. 在名称框输入：涨幅评分
3. 在公式输入框输入：`Max(0, Min(100, 100 - (收盘价/Min(收盘价, 252) - 1) * 200))`
4. 其他设置同前面函数
5. 说明框输入：涨幅评分函数，50%涨幅=0分，0%涨幅=100分，基于一年内最低价
6. 点击"保存"按钮

### 2.5 创建总评分函数
**函数配置：**
- **名称**：`总评分`
- **类型**：公式
- **公式**：`PE评分 + PB评分 + 涨幅评分`
- **显示单位**：无
- **小数位数**：1
- **说明**：三个维度评分总和，满分300分

**操作步骤：**
1. 重新点击"新建"按钮创建第四个函数
2. 在名称框输入：总评分
3. 在公式输入框输入：`PE评分 + PB评分 + 涨幅评分`
4. 小数位数选择"1"
5. 说明框输入：三个维度评分总和，满分300分
6. 点击"保存"按钮

## 第三步：设置股票池和筛选条件

### 3.1 股票池选择
1. 在策略创建页面找到"股票池"设置区域
2. 选择"全部A股"或"沪深300+中证500"
3. 勾选排除选项：
   - ☑ 排除ST股票
   - ☑ 排除停牌股票
   - ☑ 排除退市股票

### 3.2 设置筛选条件
**需要添加的筛选条件：**

**条件1：滚动市净率筛选**
1. 点击"添加筛选条件"
2. 选择指标：财务指标 → 估值指标 → 滚动市净率
3. 设置条件：滚动市净率 > 0

**条件2：市净率筛选**
1. 再次点击"添加筛选条件"
2. 选择指标：财务指标 → 估值指标 → 市净率
3. 设置条件：市净率 > 0

**条件3：总市值筛选（建议添加）**
1. 点击"添加筛选条件"
2. 选择指标：行情指标 → 总市值
3. 设置条件：总市值 > 50亿

**条件4：上市天数筛选（建议添加）**
1. 点击"添加筛选条件"
2. 选择指标：公司信息 → 上市天数
3. 设置条件：上市天数 > 365

## 第四步：设置排名条件

### 4.1 排名指标设置
1. 在"排名条件"区域点击"添加排名条件"
2. 选择指标：自定义指标 → 总评分
3. 排名方向：从大到小
4. 权重：100%

### 4.2 确认排名逻辑
- 系统将按照总评分从高到低排序选股
- 总评分越高的股票越优先买入
- 确保排名条件设置正确

## 第五步：持仓和交易设置

### 5.1 持仓参数设置
**基本持仓设置：**
- **最大持仓股票数**：50
- **调仓周期**：20天
- **个股最大仓位**：2%
- **最小买入金额**：1000元

**操作步骤：**
1. 在"持仓设置"区域找到相应参数
2. 设置最大持仓股票数为50
3. 设置调仓周期为20天
4. 设置个股最大仓位为2%

### 5.2 交易模型选择
1. 在"交易模型"设置中选择"Model II（条件卖出）"
2. 确认选择正确，这将支持我们的卖出条件设置

## 第六步：大盘择时设置

### 6.1 择时功能配置
1. 在策略设置中找到"大盘择时"或"市场择时"选项
2. 启用择时功能
3. 选择择时类型：PB择时

### 6.2 择时参数设置
**参数配置：**
- **择时指标**：大盘市净率
- **买入下限**：1.2（市净率≤1.2时满仓买入）
- **卖出上限**：2.0（市净率≥2.0时正常买入）

**操作步骤：**
1. 选择PB择时
2. 设置下限参数为1.2
3. 设置上限参数为2.0
4. 确认择时逻辑：低估值时加仓，高估值时减仓

## 第七步：卖出条件设置

### 7.1 卖出条件配置
由于平台限制，我们使用简化的卖出条件：

**卖出条件设置：**
- **卖出条件**：收益率 >= 100%
- **卖出比例**：100%
- **说明**：持仓收益达到100%时全部卖出

**操作步骤：**
1. 在"卖出条件"区域点击"添加卖出条件"
2. 选择条件类型：收益率条件
3. 设置：收益率 >= 100%
4. 卖出比例设置为100%

### 7.2 备选卖出条件（如果支持）
如果平台支持更复杂的卖出条件，可以尝试：
- 条件1：收益率 >= 80% 时卖出50%
- 条件2：收益率 >= 150% 时卖出100%

## 第八步：策略保存和命名

### 8.1 策略信息设置
**策略基本信息：**
- **策略名称**：多因子估值技术选股策略
- **策略描述**：基于PE、PB、涨幅的多因子评分选股策略，结合大盘择时和行业分散配置
- **策略标签**：多因子、估值、技术分析、择时

### 8.2 保存策略
1. 检查所有设置参数是否正确
2. 点击"保存策略"按钮
3. 确认策略保存成功
4. 记录策略ID或链接便于后续管理

## 参数设置总结

### 核心参数一览表
| 设置项目 | 参数值 | 说明 |
|---------|--------|------|
| 股票池 | 全部A股 | 排除ST、停牌股票 |
| 筛选条件 | 滚动市净率>0, 市净率>0, 总市值>50亿 | 基础质量筛选 |
| 排名指标 | 总评分 | PE+PB+涨幅评分 |
| 持仓数量 | 50只 | 确保分散化 |
| 调仓周期 | 20天 | 平衡收益和成本 |
| 个股仓位 | 2% | 风险控制 |
| 择时指标 | 大盘PB | 1.2-2.0区间 |
| 卖出条件 | 收益率≥100% | 止盈策略 |

### 关键函数公式
```
PE评分 = Max(0, Min(100, 100 - (滚动市盈率 - 10) * 100 / 15))
PB评分 = Max(0, Min(100, 100 - (市净率 - 1) * 100 / 1.5))
涨幅评分 = Max(0, Min(100, 100 - (收盘价/Min(收盘价, 252) - 1) * 200))
总评分 = PE评分 + PB评分 + 涨幅评分
```

## 注意事项和风险提示

### 操作注意事项
1. **函数创建顺序**：必须先创建PE评分、PB评分、涨幅评分，最后创建总评分
2. **语法检查**：每个函数保存前确认语法正确，无错误提示
3. **参数验证**：保存策略前检查所有参数设置
4. **权限确认**：确保账户有创建自定义函数的权限

### 风险控制建议
1. **初期小仓位**：建议首次运行时降低仓位至50-70%
2. **定期监控**：每周检查策略运行状况
3. **参数调整**：根据市场环境适时调整参数
4. **止损设置**：考虑设置组合最大回撤止损

### 常见问题解决
1. **函数语法错误**：检查括号匹配和指标名称
2. **择时功能无效**：确认择时参数设置合理
3. **选股数量异常**：检查筛选条件是否过于严格
4. **回测结果异常**：验证所有参数设置是否正确

## 下一步：回测验证

策略创建完成后，需要进行回测验证：
1. 设置回测时间段：2021-2025年
2. 选择基准指数：沪深300
3. 设置交易成本：0.3%
4. 运行回测并分析结果

完成策略创建后，请继续进行回测分析以验证策略有效性。
