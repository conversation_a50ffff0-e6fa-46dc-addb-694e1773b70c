# 果仁网策略示例与模板集合

## 概述

本文档提供了各种类型的果仁网量化策略示例和模板，涵盖不同的投资风格和策略类型，可作为策略开发的参考和起点。

## 策略分类目录

1. [价值投资策略](#价值投资策略)
2. [成长投资策略](#成长投资策略)
3. [技术分析策略](#技术分析策略)
4. [动量策略](#动量策略)
5. [反转策略](#反转策略)
6. [小市值策略](#小市值策略)
7. [行业轮动策略](#行业轮动策略)
8. [多因子策略](#多因子策略)
9. [ETF轮动策略](#ETF轮动策略)
10. [自定义指标策略](#自定义指标策略)

## 价值投资策略

### 经典低PE策略
**策略思路**：选择市盈率较低的股票，长期持有
```
股票池：全部股票
筛选条件：
- 市盈率 > 0 且 < 50
- 总市值 > 50亿
- 上市天数 > 365

排名条件：
- 市盈率 从小到大

持仓设置：
- 最大持仓股票数：20
- 调仓周期：60天
- 交易模型：模型I

大盘择时：
- MA择时（5日线，60日线）
```

### 价值成长组合策略
**策略思路**：结合价值和成长指标，选择既便宜又有成长性的股票
```
股票池：全部股票
筛选条件：
- 市盈率 > 0 且 < 30
- 净资产收益率 > 15%
- 营收增长率 > 20%
- 总市值 > 100亿

排名条件：
- 市盈率 从小到大 权重40%
- 净资产收益率 从大到小 权重30%
- 营收增长率 从大到小 权重30%

持仓设置：
- 最大持仓股票数：15
- 调仓周期：90天
```

### 低PB高ROE策略
**策略思路**：选择市净率低但净资产收益率高的股票
```
筛选条件：
- 市净率 > 0.5 且 < 3
- 净资产收益率 > 12%
- 资产负债率 < 60%

排名条件：
- 净资产收益率/市净率 从大到小

持仓设置：
- 最大持仓股票数：25
- 调仓周期：120天
```

## 成长投资策略

### 高成长策略
**策略思路**：选择营收和利润快速增长的公司
```
筛选条件：
- 营收增长率 > 30%
- 净利润增长率 > 25%
- 毛利率 > 20%
- 市盈率 < 50

排名条件：
- 营收增长率 从大到小 权重50%
- 净利润增长率 从大到小 权重50%

持仓设置：
- 最大持仓股票数：12
- 调仓周期：30天
```

### 成长质量策略
**策略思路**：在成长股中选择质量较好的公司
```
筛选条件：
- 营收增长率 > 20%
- 净利润增长率 > 15%
- 净资产收益率 > 15%
- 资产负债率 < 50%
- 经营现金流/净利润 > 0.8

排名条件：
- (营收增长率 + 净利润增长率) / 市盈率 从大到小

持仓设置：
- 最大持仓股票数：18
- 调仓周期：45天
```

## 技术分析策略

### RSI超卖策略
**策略思路**：买入RSI超卖的股票
```
筛选条件：
- RSI < 30
- 20日乖离率 < -15%
- 成交量 > 5日平均成交量 * 1.5

排名条件：
- RSI 从小到大

持仓设置：
- 最大持仓股票数：10
- 调仓周期：5天
```

### 突破策略
**策略思路**：买入突破重要技术位的股票
```
自定义指标：
- 突破信号 = if((收盘价 > Max(最高价, 20)) and (成交量 > MA(当日成交量, 20) * 2), 1, 0)

筛选条件：
- 突破信号 == 1
- 20日涨幅 > -10%

排名条件：
- 成交量/MA(当日成交量, 20) 从大到小

持仓设置：
- 最大持仓股票数：8
- 调仓周期：3天
```

### 均线多头排列策略
**策略思路**：选择均线呈多头排列的股票
```
筛选条件：
- 多头排列标记 == 1
- 收盘价 > 5日复权均价
- 5日复权均价 > 20日复权均价
- 20日复权均价 > 60日复权均价

排名条件：
- (收盘价 - 60日复权均价) / 60日复权均价 从大到小

持仓设置：
- 最大持仓股票数：15
- 调仓周期：10天
```

## 动量策略

### 价格动量策略
**策略思路**：买入近期表现强势的股票
```
自定义指标：
- 动量得分 = 20日涨幅 * 0.4 + 60日涨幅 * 0.3 + 120日涨幅 * 0.3

筛选条件：
- 20日涨幅 > 10%
- 60日涨幅 > 20%
- 成交量 > MA(当日成交量, 20)

排名条件：
- 动量得分 从大到小

持仓设置：
- 最大持仓股票数：12
- 调仓周期：15天
```

### 相对强度策略
**策略思路**：选择相对大盘表现更强的股票
```
自定义指标：
- 相对强度 = (20日股价相对涨幅 + 60日股价相对涨幅) / 2

筛选条件：
- 相对强度 > 5%
- 总市值 > 50亿

排名条件：
- 相对强度 从大到小

持仓设置：
- 最大持仓股票数：20
- 调仓周期：20天
```

## 反转策略

### 超跌反弹策略
**策略思路**：买入短期超跌但基本面良好的股票
```
筛选条件：
- 5日涨幅 < -15%
- 20日涨幅 < -25%
- 市盈率 > 0 且 < 30
- 净资产收益率 > 10%

排名条件：
- 5日涨幅 从小到大

持仓设置：
- 最大持仓股票数：10
- 调仓周期：5天
- 交易模型：模型II

卖出条件：
- 持有天数 >= 10
- 涨幅 >= 15%
```

### 长期反转策略
**策略思路**：买入长期表现较差但基本面改善的股票
```
筛选条件：
- 250日涨幅 < -30%
- 净利润增长率 > 0
- 营收增长率 > 0
- 资产负债率 < 70%

排名条件：
- 250日涨幅 从小到大 权重60%
- 净利润增长率 从大到小 权重40%

持仓设置：
- 最大持仓股票数：15
- 调仓周期：60天
```

## 小市值策略

### 经典小市值策略
**策略思路**：买入市值较小的股票，享受小盘股溢价
```
筛选条件：
- 总市值 > 10亿 且 < 100亿
- 上市天数 > 365
- 净利润 > 0

排名条件：
- 总市值 从小到大

持仓设置：
- 最大持仓股票数：30
- 调仓周期：30天

大盘择时：
- MA择时（10日线，60日线）
```

### 小市值成长策略
**策略思路**：在小市值股票中选择成长性较好的
```
筛选条件：
- 总市值 < 200亿
- 营收增长率 > 15%
- 净利润增长率 > 10%
- 上市天数 > 730

排名条件：
- 总市值 从小到大 权重50%
- 营收增长率 从大到小 权重30%
- 净利润增长率 从大到小 权重20%

持仓设置：
- 最大持仓股票数：25
- 调仓周期：45天
```

## 行业轮动策略

### 行业动量轮动
**策略思路**：买入近期表现最好的行业中的优质股票
```
自定义指标：
- 行业强度 = industry_avg(20日涨幅)

筛选条件：
- 行业强度 > 5%
- 市盈率 > 0 且 < 50
- 净资产收益率 > 10%

排名条件：
- 行业强度 从大到小 权重40%
- 20日涨幅 从大到小 权重30%
- 净资产收益率 从大到小 权重30%

持仓设置：
- 最大持仓股票数：20
- 调仓周期：20天
```

### 行业龙头策略
**策略思路**：在每个行业中选择龙头股票
```
筛选条件：
- 总市值 > industry_avg(总市值) * 2
- 营收 > industry_avg(营收) * 1.5
- 净资产收益率 > industry_avg(净资产收益率)

排名条件：
- industry_rank(总市值, 1) 从小到大

持仓设置：
- 最大持仓股票数：28
- 调仓周期：90天
```

## 多因子策略

### 综合评分策略
**策略思路**：使用多个因子进行综合评分
```
自定义指标：
- 价值得分 = (1 / 市盈率) * 100
- 成长得分 = (营收增长率 + 净利润增长率) / 2
- 质量得分 = (净资产收益率 + 毛利率) / 2
- 综合得分 = rank(价值得分) + rank(成长得分) + rank(质量得分)

筛选条件：
- 市盈率 > 0 且 < 50
- 营收增长率 > 0
- 净利润增长率 > 0
- 净资产收益率 > 8%

排名条件：
- 综合得分 从大到小

持仓设置：
- 最大持仓股票数：30
- 调仓周期：60天
```

### 风险调整收益策略
**策略思路**：选择风险调整后收益最高的股票
```
自定义指标：
- 收益风险比 = 60日涨幅 / Stdev(日收益率, 60)

筛选条件：
- 60日涨幅 > 0
- Stdev(日收益率, 60) > 0
- 总市值 > 50亿

排名条件：
- 收益风险比 从大到小

持仓设置：
- 最大持仓股票数：20
- 调仓周期：30天
```

## ETF轮动策略

### 宽基指数轮动
**策略思路**：在主要宽基指数ETF间轮动
```
基金池：静态基金池
- 510300（沪深300ETF）
- 510500（中证500ETF）
- 159915（创业板ETF）
- 510050（上证50ETF）

排名条件：
- 20日涨幅 从大到小

持仓设置：
- 最大持仓基金数：2
- 调仓周期：10天

大盘择时：
- MACD择时
```

### 行业ETF轮动
**策略思路**：在行业ETF间进行轮动
```
基金池：行业ETF池
- 512880（证券ETF）
- 512170（医疗ETF）
- 515050（5GETF）
- 512690（酒ETF）
- 等等...

排名条件：
- 10日涨幅 从大到小 权重60%
- 20日涨幅 从大到小 权重40%

持仓设置：
- 最大持仓基金数：3
- 调仓周期：5天
```

## 自定义指标策略

### Alpha因子策略
**策略思路**：使用WorldQuant Alpha因子
```
自定义指标：
- Alpha1 = argmax(power(if(1日涨幅 < 0, stdev(1日涨幅, 20), 1日涨幅), 2), 5)
- Alpha4 = percentrank(hrank(最低价, 1, 0), 9)

筛选条件：
- Alpha4 > 0.7
- 总市值 > 30亿

排名条件：
- Alpha1 从大到小 权重60%
- Alpha4 从大到小 权重40%

持仓设置：
- 最大持仓股票数：15
- 调仓周期：5天
```

### 量价关系策略
**策略思路**：基于量价关系的策略
```
自定义指标：
- 量价背离 = corr(收盘价, 当日成交量, 10)
- 放量突破 = if((收盘价 > ref(收盘价, 1)) and (当日成交量 > MA(当日成交量, 5) * 2), 1, 0)

筛选条件：
- 量价背离 < -0.3
- 放量突破 == 1

排名条件：
- 量价背离 从小到大

持仓设置：
- 最大持仓股票数：10
- 调仓周期：3天
```

## 策略优化建议

### 参数优化
1. **回测时间**：至少3-5年的历史数据
2. **样本外测试**：保留最近1年数据用于验证
3. **参数稳定性**：测试参数变化对结果的影响

### 风险控制
1. **个股仓位**：单只股票不超过总仓位的10%
2. **行业集中度**：单个行业不超过总仓位的30%
3. **止损设置**：设置合理的止损条件

### 实盘注意事项
1. **流动性**：确保选中股票有足够的流动性
2. **交易成本**：考虑实际交易成本的影响
3. **执行偏差**：预留一定的执行偏差空间

---

*本文档提供的策略示例仅供参考，实际使用时请根据市场环境和个人风险偏好进行调整。投资有风险，入市需谨慎。*
