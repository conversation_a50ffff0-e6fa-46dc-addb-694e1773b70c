# 果仁网函数完整参考手册

## 概述

本手册详细列出了果仁网平台支持的所有函数，包括自定义函数和系统函数，涵盖语法、参数说明和使用示例。这些函数可用于创建自定义指标和策略条件。

## 函数分类目录

### 基础运算类
1. [算数四则运算](#算数四则运算)
2. [逻辑函数](#逻辑函数)
3. [数学函数](#数学函数)
4. [合并函数](#合并函数)

### 时间序列类
5. [日期回溯函数](#日期回溯函数)
6. [时间窗口函数](#时间窗口函数)
7. [时序回归函数](#时序回归函数)
8. [日期统计函数](#日期统计函数)
9. [交易日函数](#交易日函数)

### 横截面分析类
10. [股票统计函数（横截面）](#股票统计函数横截面)
11. [股票池统计函数](#股票池统计函数)
12. [行业统计函数](#行业统计函数)

### 专业分析类
13. [财务指标函数](#财务指标函数)
14. [季报函数](#季报函数)
15. [技术分析函数](#技术分析函数)
16. [择时函数](#择时函数)

### 数据处理类
17. [K线聚合函数](#K线聚合函数)
18. [条件取值函数](#条件取值函数)
19. [指标数据函数](#指标数据函数)
20. [反身函数](#反身函数)
21. [分钟线计算函数](#分钟线计算函数)

### 持仓管理类
22. [持仓股票变量](#持仓股票变量)
23. [持仓统计函数](#持仓统计函数)

## 算数四则运算

### 基本运算符
- `+`：加法
- `-`：减法
- `*`：乘法
- `/`：除法

### 比较运算符
- `>`：大于
- `<`：小于
- `>=`：大于等于
- `<=`：小于等于
- `==`：等于
- `!=`：不等于

### 逻辑运算符
- `and`：逻辑与
- `or`：逻辑或
- `not`：逻辑非

### 示例
```
# 计算涨幅
涨幅 = (收盘价 - Ref(收盘价, 1)) / Ref(收盘价, 1) * 100

# 逻辑判断
强势股 = (涨幅 > 5) and (成交量 > Ref(成交量, 1) * 2)
```

## 日期回溯函数

### 基础回溯函数
- **Ref(序列, N)**：获取N日前的数据
- **BarRef(序列, N)**：获取N个交易日前的数据
- **Delta(序列, N)**：计算N日变化量，等价于序列-Ref(序列,N)

### 参数说明
- `序列`：要回溯的数据序列
- `N`：回溯的天数或交易日数

### 应用示例
```
Ref(收盘价, 5)        # 5日前的收盘价
BarRef(成交量, 10)    # 10个交易日前的成交量
Delta(收盘价, 1)      # 日涨跌额
```

### 使用注意事项
- Ref使用自然日，BarRef使用交易日
- N值不能为负数
- 当历史数据不足时返回空值

## 时间窗口函数

### 移动平均类
- **MA(序列, N)**：简单移动平均
- **EMA(序列, N)**：指数移动平均
- **DMA(序列, α)**：动态移动平均
- **SMA(序列, N, M)**：修正移动平均
- **WMA(序列, N)**：加权移动平均
- **DecayMA(序列, N)**：衰减移动平均

### 统计函数
- **Sum(序列, N)**：N日求和
- **Product(序列, N)**：N日求积
- **Max(序列, N)**：N日最大值
- **Min(序列, N)**：N日最小值
- **Med(序列, N)**：N日中位数
- **Stdev(序列, N)**：N日标准差
- **Avedev(序列, N)**：N日平均绝对偏差
- **Var(序列, N)**：N日方差
- **Zscore(序列, N)**：N日Z分数标准化

### 排名函数
- **PercentRank(序列, N)**：N日百分位排名
- **PercentRankex(序列, N)**：N日百分位排名（排除当前值）
- **Rank(序列, N)**：N日排名
- **Percentile(序列, N, P)**：N日P百分位数

### 高级统计
- **Skewness(序列, N)**：N日偏度
- **Kurt(序列, N)**：N日峰度
- **Corr(序列1, 序列2, N)**：N日相关系数
- **Covar(序列1, 序列2, N)**：N日协方差

### 双序列函数
- **MA2(序列1, 序列2, N)**：双序列移动平均
- **EMA2(序列1, 序列2, N)**：双序列指数移动平均
- **BarCorr(序列1, 序列2, N)**：N个交易日相关系数
- **BarCovar(序列1, 序列2, N)**：N个交易日协方差

### 应用示例
```
MA(收盘价, 20)                    # 20日简单移动平均
EMA(收盘价, 12)                   # 12日指数移动平均
Stdev(收盘价/Ref(收盘价,1)-1, 60) # 60日收益率标准差
PercentRank(ROE, 252)             # 一年内ROE百分位排名
```

## 时序回归函数

### 预测和趋势
- **Forcast(序列, N)**：基于N日数据的线性预测
- **Slope(序列, N)**：N日线性回归斜率
- **Neutralize(序列1, 序列2, N)**：序列1对序列2的N日中性化

### 回归统计
- **Slopexy(X序列, Y序列, N)**：N日XY回归斜率
- **Interceptxy(X序列, Y序列, N)**：N日XY回归截距
- **SSresid(X序列, Y序列, N)**：N日回归残差平方和
- **RSquare(X序列, Y序列, N)**：N日回归R平方

### 应用示例
```
Slope(收盘价, 20)                 # 20日价格趋势斜率
Forcast(营业收入, 8)              # 基于8个季度预测营业收入
RSquare(收盘价, 沪深300指数, 60)  # 60日与大盘相关性
```

## 逻辑函数

### 基础逻辑运算
- **And(条件1, 条件2, ...)**：逻辑与
- **Or(条件1, 条件2, ...)**：逻辑或
- **Not(条件)**：逻辑非

### 应用示例
```
And(收盘价>开盘价, 成交量>MA(成交量,5))  # 同时满足两个条件
Or(RSI<30, 收盘价<MA(收盘价,20)*0.95)   # 满足任一条件
Not(ST标记)                            # 非ST股票
```

## 数学函数

### 基础数学运算
- **abs(x)**：绝对值
- **sign(x)**：符号函数
- **sqrt(x)**：平方根
- **Square(x)**：平方
- **exp(x)**：指数函数
- **Power(x, n)**：幂函数

### 对数函数
- **ln(x)**：自然对数
- **log(x)**：常用对数

### 三角函数
- **sin(x)**：正弦函数
- **cos(x)**：余弦函数
- **tan(x)**：正切函数
- **asin(x)**：反正弦函数
- **acos(x)**：反余弦函数
- **atan(x)**：反正切函数

### 取整函数
- **Round(x, n)**：四舍五入到n位小数
- **Mod(x, y)**：取模运算
- **Floor(x)**：向下取整

### 特殊函数
- **Sigmoid(x)**：S型函数
- **Stdevm(序列)**：修正标准差

### 应用示例
```
abs(收盘价-MA(收盘价,20))         # 价格偏离均线的绝对值
sqrt(Var(收盘价/Ref(收盘价,1)-1, 252)) # 年化波动率
Round(ROE*100, 2)                 # ROE百分比保留2位小数
```

## 合并函数

### 条件判断
- **IF(条件, 真值, 假值)**：条件判断函数
- **Greater(x, y)**：大于判断
- **Less(x, y)**：小于判断

### 应用示例
```
IF(收盘价>MA(收盘价,20), 1, 0)         # 价格在均线上方为1，否则为0
Greater(ROE, 0.15)                     # ROE大于15%
Less(PE, 20)                          # PE小于20
```

## 金叉死叉函数

### 交叉判断
- **crossover(序列1, 序列2)**：序列1上穿序列2（金叉）
- **crossunder(序列1, 序列2)**：序列1下穿序列2（死叉）

### 应用示例
```
crossover(MA(收盘价,5), MA(收盘价,20))   # 5日线上穿20日线
crossunder(RSI, 70)                     # RSI下穿70
```

## 股票统计函数（横截面）

### 基础横截面统计
- **HMax(序列)**：横截面最大值
- **HMin(序列)**：横截面最小值
- **HAvg(序列)**：横截面平均值
- **HDemean(序列)**：横截面去均值
- **HWAvg(序列, 权重)**：横截面加权平均
- **HMed(序列)**：横截面中位数
- **HDemedian(序列)**：横截面去中位数
- **HSum(序列)**：横截面求和
- **HVar(序列)**：横截面方差
- **HStdev(序列)**：横截面标准差

### 高级横截面统计
- **Hskewness(序列)**：横截面偏度
- **Hkurt(序列)**：横截面峰度
- **HCoVar(序列1, 序列2)**：横截面协方差
- **HCorr(序列1, 序列2)**：横截面相关系数
- **HPercentile(序列, P)**：横截面P百分位数
- **HWinsorize(序列, P1, P2)**：横截面缩尾处理
- **HStandarize(序列)**：横截面标准化
- **HScale(序列)**：横截面缩放

### 横截面回归
- **HSlopeXY(X序列, Y序列)**：横截面XY回归斜率
- **HInterceptXY(X序列, Y序列)**：横截面XY回归截距
- **HNeutralize(序列1, 序列2)**：横截面中性化
- **HNeutralize2(序列1, 序列2, 序列3)**：双因子横截面中性化
- **HNeutralizeMI(序列, 行业, 市值)**：行业市值中性化

### 横截面计数和排名
- **CountStock(条件)**：满足条件的股票数量
- **HCount(序列)**：横截面非空值计数
- **HRank(序列)**：横截面排名
- **HRankScore(序列)**：横截面排名分数
- **hpercentrank(序列)**：横截面百分位排名

### 应用示例
```
HRank(PE)                             # PE横截面排名
HNeutralizeMI(收益率, 行业, 总市值)    # 行业市值中性化收益率
HPercentile(ROE, 0.8)                 # ROE的80分位数
```

## 股票池统计函数

### 股票池基础统计
- **SMax(序列, 股票池)**：股票池内最大值
- **SMin(序列, 股票池)**：股票池内最小值
- **SAvg(序列, 股票池)**：股票池内平均值
- **SWAvg(序列, 权重, 股票池)**：股票池内加权平均
- **SMed(序列, 股票池)**：股票池内中位数
- **SSum(序列, 股票池)**：股票池内求和
- **Svar(序列, 股票池)**：股票池内方差
- **SStdev(序列, 股票池)**：股票池内标准差

### 股票池高级统计
- **SSkewness(序列, 股票池)**：股票池内偏度
- **Skurt(序列, 股票池)**：股票池内峰度
- **SCovar(序列1, 序列2, 股票池)**：股票池内协方差
- **SCorr(序列1, 序列2, 股票池)**：股票池内相关系数
- **SCount(条件, 股票池)**：股票池内计数
- **SRank(序列, 股票池)**：股票池内排名
- **SRankIC(序列, 股票池)**：股票池内排名IC
- **SRankScore(序列, 股票池)**：股票池内排名分数

### 股票池处理函数
- **sbelong(股票池)**：判断是否属于股票池
- **SPercentile(序列, P, 股票池)**：股票池内P百分位数
- **SWinsorize(序列, P1, P2, 股票池)**：股票池内缩尾处理
- **SStandarize(序列, 股票池)**：股票池内标准化
- **SScale(序列, 股票池)**：股票池内缩放
- **SSlopeXY(X序列, Y序列, 股票池)**：股票池内XY回归斜率
- **SInterceptXY(X序列, Y序列, 股票池)**：股票池内XY回归截距
- **SNeutralize(序列1, 序列2, 股票池)**：股票池内中性化
- **SNeutralize2(序列1, 序列2, 序列3, 股票池)**：股票池内双因子中性化
- **Svalue(序列, 股票池)**：股票池内取值

## 行业统计函数

### 行业分析函数
- **industry_avg(指标)**：行业平均值
- **industry_rank(指标)**：行业内排名
- **industry_percentile(指标, P)**：行业内百分位数
- **industry_neutralize(指标)**：行业中性化

### 应用示例
```
industry_rank(ROE)                    # ROE行业内排名
industry_neutralize(收益率)           # 行业中性化收益率
```

## 财务指标函数

### 基础财务指标
- **ROE**：净资产收益率
- **ROA**：总资产收益率
- **PE**：市盈率
- **PB**：市净率
- **PS**：市销率
- **EPS**：每股收益
- **BVPS**：每股净资产

### 成长性指标
- **revenue_growth**：营收增长率
- **profit_growth**：利润增长率
- **eps_growth**：EPS增长率

### 财务质量指标
- **debt_ratio**：资产负债率
- **current_ratio**：流动比率
- **quick_ratio**：速动比率

## 季报函数

### 季报回溯
- **RefQ(序列, N)**：N个季度前的数据
- **TTM(序列)**：滚动十二个月数据

### 季报统计
- **SumQ(序列, N)**：N个季度求和
- **AvgQ(序列, N)**：N个季度平均
- **MaxQ(序列, N)**：N个季度最大值
- **MinQ(序列, N)**：N个季度最小值
- **MedQ(序列, N)**：N个季度中位数
- **StdevQ(序列, N)**：N个季度标准差

### 季报分析
- **ForecastQ(序列, N)**：基于N个季度的预测
- **SlopeQ(序列, N)**：N个季度趋势斜率
- **SlopexyQ(X序列, Y序列, N)**：N个季度XY回归斜率
- **NeutralizeQ(序列1, 序列2, N)**：N个季度中性化
- **CorrQ(序列1, 序列2, N)**：N个季度相关系数

### 季报处理
- **Annual(序列)**：年化处理
- **AccuQ(序列, N)**：N个季度累计

### 应用示例
```
RefQ(营业收入, 4)                      # 去年同期营业收入
TTM(净利润)                            # 滚动十二个月净利润
SumQ(营业收入, 4)                      # 近4个季度营业收入总和
SlopeQ(ROE, 8)                         # 近8个季度ROE趋势
```

## 技术分析函数

### 经典技术指标
- **RSI(N)**：相对强弱指标
- **MACD(快线, 慢线, 信号线)**：指数平滑移动平均线
- **KDJ(N, M1, M2)**：随机指标
- **BOLL(N, P)**：布林线
- **ATR(N)**：真实波动幅度

### 自定义技术指标
- **bias(N)**：乖离率
- **roc(N)**：变动率指标
- **cci(N)**：顺势指标

## 择时函数

### 择时信号
- **timing(信号)**：择时信号函数，用于大盘择时

### 应用场景
- 大盘趋势判断
- 市场风险控制
- 仓位管理决策

## K线聚合函数

### 聚合统计
- **KFirst(序列, N)**：N日内首个值
- **KLast(序列, N)**：N日内最后值
- **KMax(序列, N)**：N日内最大值
- **KMin(序列, N)**：N日内最小值
- **KSum(序列, N)**：N日内求和
- **Kavg(序列, N)**：N日内平均值

## 日期统计函数

### 计数函数
- **CountDays(条件, N)**：N日内满足条件的天数
- **CountBars(条件, N)**：N个交易日内满足条件的交易日数
- **DaysLast(条件)**：距离上次满足条件的天数
- **DaysLastN(条件, N)**：距离第N次满足条件的天数
- **BarsLast(条件)**：距离上次满足条件的交易日数
- **BarsLastN(条件, N)**：距离第N次满足条件的交易日数

### 极值位置
- **Argmax(序列, N)**：N日内最大值的位置
- **Argmin(序列, N)**：N日内最小值的位置

### 应用示例
```
CountDays(收盘价>开盘价, 20)           # 20日内上涨天数
BarsLast(crossover(MA(收盘价,5), MA(收盘价,20)))  # 距离上次金叉的交易日数
Argmax(收盘价, 60)                     # 60日内最高价出现的位置
```

## 条件取值函数

### 最后有效值
- **LastValue(序列)**：序列的最后一个有效值
- **LastValueN(序列, N)**：序列的倒数第N个有效值

### 应用示例
```
LastValue(年报ROE)                     # 最新年报ROE
LastValueN(季报营业收入, 4)            # 去年同期季报营业收入
```

## 指标数据函数

### 数据获取
- **TickerValue(指标, 股票代码)**：获取指定股票的指标值
- **IsNULL(序列)**：判断是否为空值
- **IfNULL(序列, 替换值)**：空值替换

### 应用示例
```
TickerValue(收盘价, "000001.SZ")       # 获取平安银行收盘价
IfNULL(ROE, 0)                         # ROE为空时用0替换
```

## 交易日函数

### 日期提取
- **DayW()**：星期几（1-7）
- **DayM()**：月份中的第几天
- **DayQ()**：季度中的第几天
- **DayY()**：年份中的第几天
- **MonthY()**：年份中的第几个月
- **Year()**：年份

### 应用示例
```
DayW() == 1                            # 周一
MonthY() == 12                         # 12月
Year() >= 2020                         # 2020年及以后
```

## 反身函数

### 股票属性
- **ticker()**：当前股票代码
- **industry()**：所属行业
- **sector()**：所属板块

### 应用示例
```
industry() == "银行"                   # 银行行业股票
sector() == "金融"                     # 金融板块股票
```

## 分钟线计算函数

### 级别函数
- **Level(参数)**：用于分钟线级别的计算

### 应用场景
- 高频数据分析
- 日内交易策略
- 分钟级别指标计算

## 持仓股票变量

### 持仓相关变量
- **position_ratio**：持仓比例
- **position_value**：持仓市值
- **position_cost**：持仓成本
- **position_profit**：持仓盈亏

## 持仓统计函数

### 持仓分析函数
- **portfolio_return**：组合收益率
- **portfolio_volatility**：组合波动率
- **portfolio_sharpe**：组合夏普比率
- **max_drawdown**：最大回撤

## 函数使用最佳实践

### 性能优化
1. **避免过度嵌套**：减少函数嵌套层数
2. **合理选择窗口期**：根据需要选择合适的N值
3. **缓存计算结果**：避免重复计算相同表达式

### 数据处理
1. **空值处理**：使用IfNULL处理空值情况
2. **异常值处理**：使用Winsorize函数处理极值
3. **标准化处理**：使用Standardize或Scale函数

### 逻辑设计
1. **条件组合**：合理使用And、Or、Not组合条件
2. **分支处理**：使用IF函数处理不同情况
3. **边界检查**：注意函数参数的有效范围

## 常用函数组合模式

### 技术分析模式
```
# 均线多头排列
And(MA(收盘价,5)>MA(收盘价,10), MA(收盘价,10)>MA(收盘价,20))

# RSI超买超卖
Or(RSI<30, RSI>70)

# 布林带突破
Or(收盘价>MA(收盘价,20)+2*Stdev(收盘价,20), 收盘价<MA(收盘价,20)-2*Stdev(收盘价,20))
```

### 基本面分析模式
```
# ROE稳定增长
And(ROE>0.15, SlopeQ(ROE,8)>0)

# 营收增长加速
Greater(Delta(TTM(营业收入),252)/RefQ(TTM(营业收入),4), 0.2)

# 估值合理
And(PE>0, PE<25, PB>0, PB<3)
```

### 风险控制模式
```
# 波动率控制
Less(Stdev(收盘价/Ref(收盘价,1)-1, 60), 0.03)

# 流动性筛选
Greater(MA(成交额,20), 50000000)

# 财务健康
And(资产负债率<0.7, 流动比率>1.2)
```

## 总结

果仁网提供了超过200个函数，涵盖了量化投资的各个方面：

### 函数分类统计
- **时间序列函数**：40+个，处理历史数据和趋势分析
- **横截面函数**：30+个，进行股票间比较和排名
- **数学统计函数**：50+个，提供各种数学运算和统计分析
- **逻辑条件函数**：20+个，实现复杂的条件判断
- **专业分析函数**：60+个，支持财务分析和技术分析

### 核心优势
1. **完整性**：覆盖量化投资的所有计算需求
2. **专业性**：针对中国A股市场特点设计
3. **易用性**：函数命名直观，参数设计合理
4. **高效性**：优化的计算引擎，支持大规模数据处理
5. **灵活性**：支持复杂的函数组合和嵌套使用

通过合理使用这些函数，投资者可以构建出强大的量化投资策略，实现从简单的技术指标到复杂的多因子模型的各种需求。
