# 果仁网量化投资平台完整功能文档

## 文档概述

本文档是果仁网（guorn.com）量化投资平台的完整功能参考手册，旨在为AI助手提供全面的平台知识，以便协助用户编写果仁策略和使用平台功能。

## 目录

1. [平台概述](#平台概述)
2. [核心功能](#核心功能)
3. [自定义函数详解](#自定义函数详解)
4. [策略创建流程](#策略创建流程)
5. [交易模型](#交易模型)
6. [市场择时](#市场择时)
7. [股票池管理](#股票池管理)
8. [回测与分析](#回测与分析)
9. [实盘交易](#实盘交易)
10. [常见问题解答](#常见问题解答)

## 平台概述

### 果仁网简介
果仁网是国内最活跃的非编程量化平台，为用户提供策略开发、策略分享、策略组合交易管理一站式服务。平台特点：
- **零门槛**：有选股软件使用经验的用户可以零门槛学习使用
- **非编程**：菜单界面操作，无需编程知识
- **海量因子**：提供丰富的选股指标和技术指标
- **准确回测**：使用精准的历史数据，避免未来函数

### 主要功能
1. **快速策略开发**：非编程、菜单界面、海量因子、自动参数调优
2. **策略商城**：策略达人制作的高质量策略
3. **策略组合**：多策略间实现最优配置
4. **实盘管家**：产生实时调仓指令，一键交易
5. **交易社区**：共享实盘心得、策略、量化知识

### 投资品种
- 国内上市的A股和ETF
- 未来将开放期货、港股策略平台

## 核心功能

### 选股系统

#### 选股流程
果仁使用"先筛选后排名"的选股流程：
1. **筛选条件**：从几千只股票中选出候选股票
2. **排名条件**：对候选股票进行排名
3. **最终选择**：选择排名最靠前的N只股票

#### 选股指标分类

##### 1. 行情指标
- **价格类**：开盘价、收盘价、最高价、最低价、前日收盘价
- **成交类**：成交金额、成交量、日均成交价
- **复权价格**：后复权收盘价、后复权均价（5日/20日/60日/120日/250日）
- **涨跌幅**：股价涨幅（1日/5日/20日/60日/120日/250日）
- **相对涨幅**：相对于上证指数的涨跌幅
- **换手率**：累计换手率（当日/5日/20日/60日/120日/250日）
- **市值相关**：总股本、流通股本、总市值、流通市值

##### 2. 技术指标
- **乖离率**：收盘价与均线的差距
- **波动率**：收益波动率（20日/60日/120日/250日）
- **MACD**：MACD_DIFF、MACD_DEA、MACD柱状值
- **RSI**：14日相对强弱指标
- **BBIC**：多空指标
- **CCI**：14日顺势指标
- **历史贝塔**：与沪深300的相关程度
- **布林线**：上线、下线
- **ATR**：平均真实波动范围

##### 3. 财务指标
- **盈利能力**：市盈率、净资产收益率、毛利率等
- **成长能力**：营收增长率、净利润增长率等
- **偿债能力**：资产负债率、流动比率等
- **运营能力**：总资产周转率、存货周转率等

##### 4. WQAlpha101因子
WorldQuant公布的101个Alpha因子，分为三类：
- **价格类因子**：基于股票价格的反转策略
- **量价类因子**：基于成交量和价格关系的策略
- **二分类因子**：输出0或1的分类因子

### 自定义指标系统

#### 创建规则
1. **命名规则**：只能包含中文、英文、数字，不能包含空格、标点符号
2. **表达式规则**：
   - 不能直接使用负数，如"-1"需写成"0-1"
   - 不能使用%号，如"5%"需写成"0.05"
   - 函数名大小写不敏感
   - 支持函数嵌套和自定义指标嵌套

#### 函数分类详解

##### 算数运算
- 基本运算：+、-、*、/
- 比较运算：>、<、>=、<=、==、!=
- 逻辑运算：and、or、not

##### 日期回溯函数
- `Ref(指标, N)`：获取N日前的指标值
- `BarRef(指标, N)`：获取N个交易日前的指标值
- `Delta(指标, N)`：当前值与N日前值的差

##### 时间窗口函数
- `MA(指标, N)`：N日移动平均
- `EMA(指标, N)`：N日指数移动平均
- `SMA(指标, N, M)`：加权移动平均
- `DMA(指标, 权重)`：动态移动平均
- `WMA(指标, N)`：加权移动平均
- `Sum(指标, N)`：N日累计和
- `Product(指标, N)`：N日累计乘积
- `Max(指标, N)`：N日最大值
- `Min(指标, N)`：N日最小值
- `Stdev(指标, N)`：N日标准差
- `Var(指标, N)`：N日方差
- `Med(指标, N)`：N日中位数

##### 时序回归函数
- `Forecast(Y指标, X指标, N)`：线性回归预测
- `Slope(Y指标, X指标, N)`：回归斜率
- `Neutralize(指标, 基准指标, N)`：中性化处理

##### 逻辑函数
- `if(条件, 真值, 假值)`：条件判断
- `and(条件1, 条件2)`：逻辑与
- `or(条件1, 条件2)`：逻辑或
- `not(条件)`：逻辑非

##### 数学函数
- `abs(指标)`：绝对值
- `log(指标)`：自然对数
- `sqrt(指标)`：平方根
- `power(指标, 幂次)`：幂运算
- `sign(指标)`：符号函数

##### 排名函数
- `rank(指标)`：横截面排名
- `percentrank(指标, N)`：N日内百分位排名
- `hrank(指标, 方向, 类型)`：行业内排名

##### 相关性函数
- `corr(指标1, 指标2, N)`：N日相关系数
- `covar(指标1, 指标2, N)`：N日协方差

## 策略创建流程

### 1. 选股设置

#### 股票池选择
- **我的股票池**：用户自定义的股票池
- **系统股票池**：5G概念、次新股票、大数据等主题股票池
- **指数成份**：各大指数成分股
- **行业筛选**：申万2014/2021行业分类
- **板块筛选**：主板、中小板、创业板、科创板

#### 筛选条件设置
1. 选择选股指标
2. 设置比较符（>、<、>=、<=、==、!=）
3. 选择比较范围（全部、行业内、指数内等）
4. 设置具体数值

#### 排名条件设置
1. 选择排名指标
2. 设置排名方向（从大到小、从小到大）
3. 设置权重（支持多指标加权排名）

### 2. 交易模型选择

#### 模型I：定期轮动模型
- 每个调仓日卖出所有股票
- 重新等权重买入选中股票
- 适合快速轮动短周期策略

#### 模型II：条件卖出模型
- 必须设置卖出条件
- 只卖出满足卖出条件的股票
- 支持个股理想仓位设置
- 交易频率较低，更灵活

### 3. 大盘择时设置

#### 趋势择时
- **MA择时**：移动平均线金叉死叉
- **MACD择时**：MACD指标金叉死叉
- **DMA择时**：平均线差指标
- **TRIX择时**：三重指数平滑移动平均

#### 反转择时
- **PE择时**：基于市盈率的抄底逃顶
- **PB择时**：基于市净率的抄底逃顶

### 4. 股指对冲设置
- 选择对冲股指（上证50、沪深300、中证500）
- 设置保证金比例
- 设置月贴水率
- 设置对冲比例

## 交易模型详解

### 模型I特点
- **定期调仓**：按设定周期调仓
- **重新平衡**：每次调仓重新平衡仓位
- **等权重**：所有股票等权重持有
- **简单易懂**：适合新手使用

### 模型II特点
- **条件卖出**：只有满足条件才卖出
- **理想仓位**：设置个股理想仓位比例
- **仓位偏离**：允许一定范围内的仓位偏离
- **买入限制**：可设置额外的买入限制条件

### 卖出条件类型
1. **排名卖出**：排名下降到指定位置
2. **持有天数**：持有超过指定天数
3. **止盈条件**：收益达到指定比例
4. **止损条件**：亏损达到指定比例

## 市场择时详解

### 择时原理
用个股指标选个股，用大盘指标择时，是行之有效的股票策略。

### 趋势择时指标

#### MA金叉死叉
```
DMA = MA短线 - MA长线
金叉条件: MA短线(t) > MA短线(t-1) and DMA(t) > 0 and DMA(t-1) < 0
死叉条件: MA短线(t) < MA短线(t-1) and DMA(t) < 0 and DMA(t-1) > 0
```

#### MACD金叉死叉
```
DIF = EMA短线 - EMA长线
DEA = EMA(DIF, M)
金叉条件: DIF(t) > 0 and DIF(t) > DIF(t-1) and DIF(t) > DEA(t) and DIF(t-1) < DEA(t-1)
死叉条件: DIF(t) < 0 and DIF(t) < DIF(t-1) and DIF(t) < DEA(t) and DIF(t-1) > DEA(t-1)
```

### 反转择时
- **PE择时**：市盈率低于下限时买入，高于上限时卖出
- **PB择时**：市净率低于下限时买入，高于上限时卖出

### 组合择时
可同时使用多个择时条件，分别设置牛市转换和熊市转换需要满足的条件数量。

## 股票池管理

### 股票池类型

#### 1. 静态股票池
- 手工输入股票代码
- 适合固定股票轮动策略
- 示例：四大银行轮动

#### 2. 动态股票池
- 根据选股条件动态生成
- 股票清单每日变化
- 适合分层量化策略

#### 3. 合成股票池
- 实现股票池间的集合操作
- 主要用于黑名单功能
- 格式：股票全集 - 黑名单股票池

### 使用示例

#### 四大银行轮动策略
1. 创建静态股票池，包含：工商银行(601398)、建设银行(601939)、农业银行(601288)、中国银行(601988)
2. 设置选股条件：市盈率最小
3. 持仓数量：1只
4. 调仓周期：20天

#### 排除特定股票策略
1. 创建黑名单股票池
2. 创建合成股票池：全部股票 - 黑名单
3. 在此基础上设置选股策略

## 回测与分析

### 回测设置
- **回测时间**：设置起始和结束日期
- **收益基准**：选择对比基准（沪深300、中证500等）
- **交易成本**：设置单边交易成本
- **排除时间段**：排除特定时间段

### 回测结果分析
- **收益曲线**：策略收益与基准对比
- **风险指标**：最大回撤、夏普比率、波动率
- **持仓详情**：每个调仓日的具体持仓
- **调仓记录**：买入卖出的详细记录

### 策略评分
策略评分由5个成分组成：
1. **收益分**：基于年化收益率
2. **抗风险分**：基于最大回撤率和胜率
3. **流动性分**：策略容量评估
4. **稳定性分**：收益波动率
5. **实盘分**：实盘时间和相对收益

## 实盘交易

### 调仓指令

#### 模型I调仓信号
- **卖出**：不符合选股条件的股票
- **持有**：继续符合条件的股票
- **买入**：新选中的股票
- **持有（备选）**：备选持有股票
- **买入（备选）**：备选买入股票

#### 模型II调仓信号
- **卖出**：满足卖出条件的股票
- **减仓至目标仓位**：仓位过高需要减仓
- **持有**：仓位在合理范围内
- **加仓至目标仓位**：仓位过低需要加仓
- **买入**：新买入股票

### 停牌和涨跌停处理
- **停牌股票**：无法买卖，保持原仓位
- **一字涨停**：无法买入
- **一字跌停**：无法卖出
- **备选机制**：自动选择备选股票

## 常见问题解答

### 1. 数据相关
**Q: 选股指标的数据从哪里来？**
A: 果仁根据原始市场数据和公司财报自己计算，大多数指标从2007年1月4日开始，股票数据每晚10点更新。

**Q: 为什么只有后复权价格？**
A: 前复权价格包含未来信息，为防止使用未来函数，果仁只提供后复权价格作为选股指标。

### 2. 策略相关
**Q: 筛选条件和排名条件有什么区别？**
A: 筛选条件用于粗选候选股票，排名条件用于对候选股票精确排名，建议先筛选后排名。

**Q: 如何选择交易模型？**
A: 模型I适合新手和快速轮动策略，模型II适合实盘用户和低频交易策略。

### 3. 回测相关
**Q: 果仁历史回测真实吗？**
A: 是的，果仁使用精准的历史数据，处理了停牌、涨跌停、交易成本等真实情况。

**Q: 为什么回测起始日变化收益差别很大？**
A: 可能是统计样本过少或策略过度拟合，建议增加持仓股票数或延长回测时间。

### 4. 技术相关
**Q: 果仁支持什么浏览器？**
A: 建议使用Chrome，也支持Opera、360极速模式、Safari和高版本IE。

**Q: 如何理解各种功能？**
A: 使用"每日选股"查看选股结果，查看"调仓详情"了解策略运作情况。

## 自定义函数完整列表

### 股票统计函数
- `industry_rank(指标, 方向)`：行业内排名
- `industry_neutralize(指标)`：行业中性化
- `industry_avg(指标)`：行业平均值
- `industry_max(指标)`：行业最大值
- `industry_min(指标)`：行业最小值

### 财务指标函数
- `ttm(季报指标)`：最近四个季度累计值
- `quarter_growth(指标, N)`：季度同比增长率
- `year_growth(指标, N)`：年度同比增长率

### 持仓股票变量（用于交易模型II）
- `$beginPrice`：后复权买入价
- `$Days`：持有天数
- `$change`：买入后涨幅
- `$withdraw`：买入后最高点跌幅
- `$beginRank`：买入时排名
- `$beginScore`：买入时排名分
- `$rank`：最新排名
- `$score`：最新排名分

### 持仓统计函数
- `$max(指标, 股票池代码)`：持仓股票指标最大值
- `$min(指标, 股票池代码)`：持仓股票指标最小值
- `$sum(指标, 股票池代码)`：持仓股票指标总和
- `$avg(指标, 股票池代码)`：持仓股票指标平均值

股票池代码说明：
- 1：持仓股票池（默认）
- 2：经过筛选条件的股票池
- 3：候选买入股票池（经过筛选但不在持仓中）

### K线聚合函数
将日线级别指标调整到周线或月线级别：
- `KFirst(指标, 高K线级别)`：高K线的第一个值
- `KLast(指标, 高K线级别)`：高K线的最后一个值
- `KMax(指标, 高K线级别)`：高K线的最高值
- `KMin(指标, 高K线级别)`：高K线的最低值
- `KSum(指标, 高K线级别)`：高K线的和值

高K线级别：
- y：年线
- m：月线
- w：周线

示例：
- `KLast(收盘价, m)`：月线收盘价
- `ref(KLast(收盘价, m), 1)`：上个月的月线收盘价
- `MA(KLast(收盘价, m), 20)`：20个月的月线收盘价均价

### 大盘择时函数
- `Timing(指标, 下限, 上限)`：择时信号生成
  - 指标小于下限时返回1（买入信号）
  - 指标大于上限时返回0（卖出信号）
  - 其他情况保持上一天的值

## 策略示例

### 示例1：小市值策略
```
筛选条件：
- 总市值 > 10亿
- 上市天数 > 365

排名条件：
- 总市值 从小到大

持仓设置：
- 最大持仓股票数：10
- 调仓周期：20天
```

### 示例2：价值成长策略
```
筛选条件：
- 市盈率 > 0 且 < 30
- 净资产收益率 > 10%
- 营收增长率 > 15%

排名条件：
- 市盈率 从小到大 权重50%
- 净资产收益率 从大到小 权重30%
- 营收增长率 从大到小 权重20%

持仓设置：
- 最大持仓股票数：15
- 调仓周期：30天
```

### 示例3：技术指标策略
```
筛选条件：
- RSI < 30（超卖）
- 20日乖离率 < -10%
- 成交量 > 5日平均成交量

排名条件：
- RSI 从小到大
- 20日乖离率 从小到大

持仓设置：
- 最大持仓股票数：8
- 调仓周期：5天
```

### 示例4：自定义指标策略
```
自定义指标：
- 动量指标 = (收盘价 / ref(收盘价, 20) - 1) * 100
- 相对强度 = rank(动量指标) / count(动量指标)

筛选条件：
- 动量指标 > 5
- 相对强度 > 0.8

排名条件：
- 相对强度 从大到小
```

## 高级功能

### 分钟线回测
果仁支持分钟线级别的回测，可以更精确地模拟交易：
- 支持1分钟、5分钟、15分钟、30分钟、60分钟K线
- 可以设置具体的买卖时点
- 更准确的成交价格模拟

### 组合策略
- 可以创建多个子策略
- 设置各子策略的权重
- 实现策略间的风险分散
- 支持动态权重调整

### 风险控制
- 个股最大仓位限制
- 行业集中度控制
- 最大回撤控制
- 止损止盈设置

### 实盘跟踪
- 微信服务号推送调仓指令
- 实盘收益跟踪
- 与回测收益对比分析
- 实盘偏差分析

## 数据说明

### 数据来源
- **行情数据**：实时股票价格、成交量等
- **财务数据**：上市公司财报数据
- **指数数据**：各大指数成分股和权重
- **行业分类**：申万行业分类标准

### 数据更新时间
- **股票数据**：每晚10点完成更新
- **基金数据**：第二日早9点完成更新
- **财务数据**：财报发布后及时更新

### 数据质量保证
- 使用point-in-time数据，避免未来函数
- 处理股票分红、拆股、配股
- 考虑停牌、涨跌停等真实情况
- 准确的交易成本计算

## 平台限制与注意事项

### 使用限制
- 策略回测最早从2007年开始
- 单个策略最多持仓100只股票
- 自定义指标表达式长度限制
- 部分高级功能需要付费

### 注意事项
1. **避免过度拟合**：不要过度优化历史数据
2. **样本充足性**：确保回测样本数量足够
3. **交易成本**：考虑实际交易成本对收益的影响
4. **流动性风险**：注意小市值股票的流动性问题
5. **市场环境变化**：历史表现不代表未来收益

### 最佳实践
1. **多时段验证**：在不同市场环境下验证策略
2. **样本外测试**：保留部分数据用于样本外验证
3. **风险分散**：不要过度集中于某个行业或风格
4. **定期调整**：根据市场变化适时调整策略参数
5. **实盘验证**：小资金实盘验证后再大规模应用

## 联系方式

- **QQ群**：479478202
- **微信公众号**：果仁订阅号
- **官方网站**：https://guorn.com
- **客服**：网站内私信客服

---

*本文档基于果仁网官方帮助文档整理，包含平台所有核心功能和自定义函数的详细说明，旨在为AI助手提供完整的果仁网知识库，以便更好地协助用户进行量化投资策略开发。*
